apiVersion: v1
kind: ConfigMap
metadata:
  name: oracle-schema-check-script
  namespace: default
data:
  oracle-schema-check.py: |
    #!/usr/bin/env python3
    """
    Oracle Database Schema Diagnostic Tool
    Checks for missing tables and provides schema information
    """

    import os
    import sys
    import time
    import logging
    from datetime import datetime

    try:
        import cx_Oracle
    except ImportError:
        print("ERROR: cx_Oracle library not found. Please install it first.")
        print("Run: pip install cx_Oracle")
        sys.exit(1)

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)

    def get_db_config():
        """Get database configuration from environment variables"""
        config = {
            'host': os.getenv('DB_HOST', '*************'),
            'port': int(os.getenv('DB_PORT', '1521')),
            'service_name': os.getenv('DB_SERVICE_NAME', 'TEST18C'),
            'username': os.getenv('DB_USERNAME', 'dev_ESERVICES'),
            'password': os.getenv('DB_PASSWORD', 'dev_ESERVICES')
        }
        
        logger.info(f"Database config: {config['username']}@{config['host']}:{config['port']}/{config['service_name']}")
        return config

    def check_oracle_schema():
        """Check Oracle database schema and missing tables"""
        config = get_db_config()
        
        # Create connection string
        dsn = cx_Oracle.makedsn(
            host=config['host'],
            port=config['port'],
            service_name=config['service_name']
        )
        
        connection = None
        try:
            logger.info("Connecting to Oracle database...")
            connection = cx_Oracle.connect(
                user=config['username'],
                password=config['password'],
                dsn=dsn
            )
            
            logger.info("✅ Successfully connected to Oracle database")
            cursor = connection.cursor()
            
            # Check current user and schema
            cursor.execute("SELECT USER FROM DUAL")
            current_user = cursor.fetchone()[0]
            logger.info(f"📋 Current user: {current_user}")
            
            # List all tables accessible to current user
            logger.info("📊 Checking accessible tables...")
            cursor.execute("""
                SELECT table_name, owner 
                FROM all_tables 
                WHERE owner IN (USER, 'DEV_ESERVICES', 'ESERVICES', 'PUBLIC')
                ORDER BY owner, table_name
            """)
            
            tables = cursor.fetchall()
            logger.info(f"📈 Found {len(tables)} accessible tables")
            
            # Group tables by owner
            tables_by_owner = {}
            for table_name, owner in tables:
                if owner not in tables_by_owner:
                    tables_by_owner[owner] = []
                tables_by_owner[owner].append(table_name)
            
            # Display tables by owner
            for owner, table_list in tables_by_owner.items():
                logger.info(f"📁 Schema '{owner}': {len(table_list)} tables")
                for table in sorted(table_list)[:10]:  # Show first 10 tables
                    logger.info(f"   - {table}")
                if len(table_list) > 10:
                    logger.info(f"   ... and {len(table_list) - 10} more tables")
            
            # Check for specific missing tables from the error logs
            missing_tables = [
                'IPROD_LOCATION',
                'IPROD_ELEVATOR_BUIDING_CATEGORY'
            ]
            
            logger.info("🔍 Checking for specific missing tables...")
            for table_name in missing_tables:
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM all_tables 
                    WHERE table_name = :table_name
                    AND owner IN (USER, 'DEV_ESERVICES', 'ESERVICES', 'PUBLIC')
                """, {'table_name': table_name})
                
                count = cursor.fetchone()[0]
                if count > 0:
                    logger.info(f"✅ Table '{table_name}' exists")
                    
                    # Get table owner
                    cursor.execute("""
                        SELECT owner 
                        FROM all_tables 
                        WHERE table_name = :table_name
                        AND owner IN (USER, 'DEV_ESERVICES', 'ESERVICES', 'PUBLIC')
                    """, {'table_name': table_name})
                    
                    owner = cursor.fetchone()[0]
                    logger.info(f"   📍 Owner: {owner}")
                    
                    # Check if table has data
                    try:
                        cursor.execute(f'SELECT COUNT(*) FROM "{owner}"."{table_name}"')
                        row_count = cursor.fetchone()[0]
                        logger.info(f"   📊 Row count: {row_count}")
                    except Exception as e:
                        logger.warning(f"   ⚠️  Cannot access table data: {e}")
                        
                else:
                    logger.error(f"❌ Table '{table_name}' NOT FOUND")
                    
                    # Search for similar table names
                    cursor.execute("""
                        SELECT table_name, owner 
                        FROM all_tables 
                        WHERE table_name LIKE :pattern
                        AND owner IN (USER, 'DEV_ESERVICES', 'ESERVICES', 'PUBLIC')
                    """, {'pattern': f'%{table_name.split("_")[-1]}%'})
                    
                    similar_tables = cursor.fetchall()
                    if similar_tables:
                        logger.info(f"   🔍 Similar tables found:")
                        for sim_table, sim_owner in similar_tables:
                            logger.info(f"      - {sim_owner}.{sim_table}")
            
            # Check user privileges
            logger.info("🔐 Checking user privileges...")
            cursor.execute("""
                SELECT privilege 
                FROM user_sys_privs 
                WHERE privilege IN ('CREATE TABLE', 'CREATE VIEW', 'SELECT ANY TABLE')
                ORDER BY privilege
            """)
            
            privileges = cursor.fetchall()
            if privileges:
                logger.info("   User system privileges:")
                for priv in privileges:
                    logger.info(f"   - {priv[0]}")
            else:
                logger.warning("   No relevant system privileges found")
            
            # Check role privileges
            cursor.execute("""
                SELECT granted_role 
                FROM user_role_privs 
                ORDER BY granted_role
            """)
            
            roles = cursor.fetchall()
            if roles:
                logger.info("   User roles:")
                for role in roles:
                    logger.info(f"   - {role[0]}")
            
            cursor.close()
            return True
            
        except cx_Oracle.DatabaseError as e:
            error, = e.args
            logger.error(f"❌ Database error: {error.message}")
            return False
            
        except Exception as e:
            logger.error(f"❌ Unexpected error: {str(e)}")
            return False
            
        finally:
            if connection:
                connection.close()
                logger.info("Database connection closed")

    def main():
        """Main function"""
        logger.info("Starting Oracle Database Schema Check")
        logger.info(f"Check started at: {datetime.now()}")
        
        success = check_oracle_schema()
        
        if success:
            logger.info("🎉 Oracle database schema check completed")
            sys.exit(0)
        else:
            logger.error("💥 Oracle database schema check failed")
            sys.exit(1)

    if __name__ == "__main__":
        main()

---
apiVersion: v1
kind: Secret
metadata:
  name: oracle-db-secret
  namespace: default
type: Opaque
stringData:
  DB_HOST: "*************"
  DB_PORT: "1521"
  DB_SERVICE_NAME: "TEST18C"
  DB_USERNAME: "dev_ESERVICES"
  DB_PASSWORD: "dev_ESERVICES"

---
apiVersion: batch/v1
kind: Job
metadata:
  name: oracle-schema-check
  namespace: default
spec:
  template:
    spec:
      restartPolicy: Never
      containers:
      - name: schema-check
        image: python:3.9-slim
        imagePullPolicy: Always
        command: ["/bin/bash"]
        args: ["-c", "apt-get update && apt-get install -y wget unzip libaio1t64 && ln -sf /usr/lib/x86_64-linux-gnu/libaio.so.1t64 /usr/lib/x86_64-linux-gnu/libaio.so.1 && wget -q https://download.oracle.com/otn_software/linux/instantclient/1923000/instantclient-basic-linux.x64-*********.0dbru.zip -O /tmp/oracle.zip && unzip -q /tmp/oracle.zip -d /opt/oracle && echo '/opt/oracle/instantclient_19_23' > /etc/ld.so.conf.d/oracle-instantclient.conf && ldconfig && pip install cx_Oracle==8.3.0 && python /app/oracle-schema-check.py"]
        volumeMounts:
        - name: schema-check-script
          mountPath: /app
        env:
        - name: DB_CONNECTION
          value: "oracle"
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: oracle-db-secret
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: oracle-db-secret
              key: DB_PORT
        - name: DB_SERVICE_NAME
          valueFrom:
            secretKeyRef:
              name: oracle-db-secret
              key: DB_SERVICE_NAME
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: oracle-db-secret
              key: DB_USERNAME
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: oracle-db-secret
              key: DB_PASSWORD
        - name: LD_LIBRARY_PATH
          value: "/opt/oracle/instantclient_19_23"
        - name: PATH
          value: "/opt/oracle/instantclient_19_23:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: schema-check-script
        configMap:
          name: oracle-schema-check-script
  backoffLimit: 1
