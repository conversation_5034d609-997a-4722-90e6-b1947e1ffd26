#!/bin/bash

set -e

echo "🚀 Oracle Database Connectivity Test for AKS"
echo "============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker first."
    exit 1
fi

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if connected to AKS cluster
if ! kubectl cluster-info > /dev/null 2>&1; then
    print_error "Not connected to Kubernetes cluster. Please configure kubectl first."
    print_warning "Run: az aks get-credentials --resource-group <resource-group> --name <cluster-name>"
    exit 1
fi

print_status "Building Docker image..."
docker build -t oracle-test:latest .

print_status "Applying Kubernetes manifests..."
kubectl apply -f k8s-oracle-test.yaml

print_status "Waiting for secret to be created..."
kubectl wait --for=condition=complete --timeout=30s secret/oracle-db-secret || true

print_status "Starting connectivity test as a Job..."
kubectl delete job oracle-connectivity-test --ignore-not-found=true
kubectl apply -f k8s-oracle-test.yaml

print_status "Waiting for job to complete (timeout: 120s)..."
kubectl wait --for=condition=complete --timeout=120s job/oracle-connectivity-test

print_status "Getting job logs..."
JOB_POD=$(kubectl get pods --selector=job-name=oracle-connectivity-test -o jsonpath='{.items[0].metadata.name}')
kubectl logs $JOB_POD

print_status "Job status:"
kubectl get job oracle-connectivity-test

echo ""
print_status "Test completed! Check the logs above for results."
echo ""
print_warning "To run the test again, execute: ./deploy.sh"
print_warning "To clean up resources, run: kubectl delete -f k8s-oracle-test.yaml"
