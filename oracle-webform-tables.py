#!/usr/bin/env python3
"""
Oracle WEBFORM_DB Schema Table Listing Tool
Lists all tables in WEBFORM_DB schema with detailed information
"""

import os
import sys
import time
import logging
from datetime import datetime

try:
    import cx_Oracle
except ImportError:
    print("ERROR: cx_Oracle library not found. Please install it first.")
    print("Run: pip install cx_Oracle")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_db_config():
    """Get database configuration from environment variables"""
    config = {
        'host': os.getenv('DB_HOST', 'takafull-test.c2hcooa00sjz.us-east-1.rds.amazonaws.com'),
        'port': int(os.getenv('DB_PORT', '1521')),
        'service_name': os.getenv('DB_SERVICE_NAME', 'orcl'),
        'username': os.getenv('DB_USERNAME', 'webform_db'),
        'password': os.getenv('DB_PASSWORD', 'WebForm123!@#')
    }
    
    logger.info(f"Database config: {config['username']}@{config['host']}:{config['port']}/{config['service_name']}")
    return config

def list_webform_tables():
    """List all tables in WEBFORM_DB schema with detailed information"""
    config = get_db_config()
    
    # Create connection string
    dsn = cx_Oracle.makedsn(
        host=config['host'],
        port=config['port'],
        service_name=config['service_name']
    )
    
    connection = None
    try:
        logger.info("Connecting to Oracle RDS database...")
        connection = cx_Oracle.connect(
            user=config['username'],
            password=config['password'],
            dsn=dsn
        )
        
        logger.info("✅ Successfully connected to Oracle RDS database")
        cursor = connection.cursor()
        
        # Check current user
        cursor.execute("SELECT USER FROM DUAL")
        current_user = cursor.fetchone()[0]
        logger.info(f"📋 Current user: {current_user}")
        
        # Get all tables in WEBFORM_DB schema
        logger.info("📊 Listing all tables in WEBFORM_DB schema...")
        cursor.execute("""
            SELECT table_name, num_rows, last_analyzed, tablespace_name, status
            FROM user_tables 
            ORDER BY table_name
        """)
        
        tables = cursor.fetchall()
        logger.info(f"📈 Found {len(tables)} tables in WEBFORM_DB schema")
        
        # Process each table
        for table_name, num_rows, last_analyzed, tablespace_name, status in tables:
            logger.info(f"\n🗂️  TABLE: {table_name}")
            logger.info(f"   📊 Rows: {num_rows if num_rows else 'No statistics'}")
            logger.info(f"   📅 Last Analyzed: {last_analyzed if last_analyzed else 'Never'}")
            logger.info(f"   💾 Tablespace: {tablespace_name if tablespace_name else 'Default'}")
            logger.info(f"   ✅ Status: {status}")
            
            # Get column information
            cursor.execute("""
                SELECT column_name, data_type, data_length, data_precision, data_scale, 
                       nullable, data_default, column_id
                FROM user_tab_columns 
                WHERE table_name = :table_name
                ORDER BY column_id
            """, {'table_name': table_name})
            
            columns = cursor.fetchall()
            logger.info(f"   📋 Columns ({len(columns)}):")
            
            for col_name, data_type, data_length, data_precision, data_scale, nullable, data_default, column_id in columns:
                # Format data type
                if data_type in ['VARCHAR2', 'CHAR']:
                    type_info = f"{data_type}({data_length})"
                elif data_type == 'NUMBER':
                    if data_precision and data_scale:
                        type_info = f"NUMBER({data_precision},{data_scale})"
                    elif data_precision:
                        type_info = f"NUMBER({data_precision})"
                    else:
                        type_info = "NUMBER"
                else:
                    type_info = data_type
                
                null_info = "NULL" if nullable == 'Y' else "NOT NULL"
                default_info = f" DEFAULT {data_default}" if data_default else ""
                
                logger.info(f"      {column_id:2d}. {col_name:<30} {type_info:<20} {null_info}{default_info}")
            
            # Get constraints
            cursor.execute("""
                SELECT constraint_name, constraint_type, search_condition, status
                FROM user_constraints 
                WHERE table_name = :table_name
                ORDER BY constraint_type, constraint_name
            """, {'table_name': table_name})
            
            constraints = cursor.fetchall()
            if constraints:
                logger.info(f"   🔒 Constraints ({len(constraints)}):")
                for const_name, const_type, search_condition, const_status in constraints:
                    type_desc = {
                        'P': 'PRIMARY KEY',
                        'R': 'FOREIGN KEY', 
                        'U': 'UNIQUE',
                        'C': 'CHECK',
                        'V': 'VIEW CHECK',
                        'O': 'READ ONLY'
                    }.get(const_type, const_type)
                    
                    condition_info = f" ({search_condition})" if search_condition and len(str(search_condition)) < 50 else ""
                    logger.info(f"      - {const_name}: {type_desc}{condition_info} [{const_status}]")
            
            # Get indexes
            cursor.execute("""
                SELECT index_name, index_type, uniqueness, status
                FROM user_indexes 
                WHERE table_name = :table_name
                ORDER BY index_name
            """, {'table_name': table_name})
            
            indexes = cursor.fetchall()
            if indexes:
                logger.info(f"   📇 Indexes ({len(indexes)}):")
                for idx_name, idx_type, uniqueness, idx_status in indexes:
                    unique_info = f" ({uniqueness})" if uniqueness != 'NONUNIQUE' else ""
                    logger.info(f"      - {idx_name}: {idx_type}{unique_info} [{idx_status}]")
            
            # Get sample data (first 3 rows)
            try:
                cursor.execute(f'SELECT * FROM "{table_name}" WHERE ROWNUM <= 3')
                sample_data = cursor.fetchall()
                
                if sample_data:
                    logger.info(f"   📄 Sample Data ({len(sample_data)} rows):")
                    # Get column names for header
                    col_names = [desc[0] for desc in cursor.description]
                    
                    for i, row in enumerate(sample_data, 1):
                        logger.info(f"      Row {i}:")
                        for col_name, value in zip(col_names, row):
                            # Truncate long values
                            str_value = str(value) if value is not None else 'NULL'
                            if len(str_value) > 50:
                                str_value = str_value[:47] + '...'
                            logger.info(f"        {col_name}: {str_value}")
                else:
                    logger.info(f"   📄 Sample Data: No data found")
                    
            except Exception as e:
                logger.warning(f"   ⚠️  Cannot access table data: {e}")
        
        # Get additional schema information
        logger.info("\n📊 WEBFORM_DB Schema Summary:")
        
        # Get total row counts
        cursor.execute("""
            SELECT SUM(num_rows) as total_rows
            FROM user_tables 
            WHERE num_rows IS NOT NULL
        """)
        
        total_rows = cursor.fetchone()[0]
        logger.info(f"   📈 Total rows (with statistics): {total_rows if total_rows else 'No statistics'}")
        
        # Get tablespace usage
        cursor.execute("""
            SELECT tablespace_name, COUNT(*) as table_count
            FROM user_tables 
            GROUP BY tablespace_name
            ORDER BY table_count DESC
        """)
        
        tablespaces = cursor.fetchall()
        if tablespaces:
            logger.info(f"   💾 Tablespace distribution:")
            for ts_name, table_count in tablespaces:
                ts_display = ts_name if ts_name else 'Default'
                logger.info(f"      - {ts_display}: {table_count} tables")
        
        cursor.close()
        return True
        
    except cx_Oracle.DatabaseError as e:
        error, = e.args
        logger.error(f"❌ Database error: {error.message}")
        return False
        
    except Exception as e:
        logger.error(f"❌ Unexpected error: {str(e)}")
        return False
        
    finally:
        if connection:
            connection.close()
            logger.info("\nDatabase connection closed")

def main():
    """Main function"""
    logger.info("Starting WEBFORM_DB Schema Table Listing")
    logger.info(f"Started at: {datetime.now()}")
    
    success = list_webform_tables()
    
    if success:
        logger.info("🎉 WEBFORM_DB table listing completed successfully")
        sys.exit(0)
    else:
        logger.error("💥 WEBFORM_DB table listing failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
