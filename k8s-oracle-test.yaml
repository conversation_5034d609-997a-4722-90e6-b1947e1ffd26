apiVersion: v1
kind: ConfigMap
metadata:
  name: oracle-test-script
  namespace: default
data:
  oracle-test.py: |
    #!/usr/bin/env python3
    """
    Oracle Database Connectivity Test for AKS
    Tests connection to Oracle database using provided credentials
    """

    import os
    import sys
    import time
    import logging
    from datetime import datetime

    try:
        import cx_Oracle
    except ImportError:
        print("ERROR: cx_Oracle library not found. Please install it first.")
        print("Run: pip install cx_Oracle")
        sys.exit(1)

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)

    def get_db_config():
        """Get database configuration from environment variables"""
        config = {
            'host': os.getenv('DB_HOST', 'takafull-test.c2hcooa00sjz.us-east-1.rds.amazonaws.com'),
            'port': int(os.getenv('DB_PORT', '1521')),
            'service_name': os.getenv('DB_SERVICE_NAME', 'orcl'),
            'username': os.getenv('DB_USERNAME', 'webform_db'),
            'password': os.getenv('DB_PASSWORD', 'WebForm123!@#')
        }

        logger.info(f"Database config: {config['username']}@{config['host']}:{config['port']}/{config['service_name']}")
        return config

    def test_oracle_connection():
        """Test Oracle database connection"""
        config = get_db_config()

        # Create connection string
        dsn = cx_Oracle.makedsn(
            host=config['host'],
            port=config['port'],
            service_name=config['service_name']
        )

        connection = None
        try:
            logger.info("Attempting to connect to Oracle database...")
            start_time = time.time()

            connection = cx_Oracle.connect(
                user=config['username'],
                password=config['password'],
                dsn=dsn
            )

            connect_time = time.time() - start_time
            logger.info(f"✅ Successfully connected to Oracle database in {connect_time:.2f} seconds")

            # Test basic query
            cursor = connection.cursor()
            cursor.execute("SELECT SYSDATE FROM DUAL")
            result = cursor.fetchone()

            logger.info(f"✅ Database query successful. Current database time: {result[0]}")

            # Get database version
            cursor.execute("SELECT BANNER FROM V$VERSION WHERE ROWNUM = 1")
            version = cursor.fetchone()
            logger.info(f"✅ Database version: {version[0]}")

            cursor.close()

            return True

        except cx_Oracle.DatabaseError as e:
            error, = e.args
            logger.error(f"❌ Database connection failed: {error.message}")
            return False

        except Exception as e:
            logger.error(f"❌ Unexpected error: {str(e)}")
            return False

        finally:
            if connection:
                connection.close()
                logger.info("Database connection closed")

    def main():
        """Main function"""
        logger.info("Starting Oracle Database Connectivity Test")
        logger.info(f"Test started at: {datetime.now()}")

        success = test_oracle_connection()

        if success:
            logger.info("🎉 Oracle database connectivity test PASSED")
            sys.exit(0)
        else:
            logger.error("💥 Oracle database connectivity test FAILED")
            sys.exit(1)

    if __name__ == "__main__":
        main()

---
apiVersion: v1
kind: Secret
metadata:
  name: oracle-db-secret
  namespace: default
type: Opaque
stringData:
  DB_HOST: "takafull-test.c2hcooa00sjz.us-east-1.rds.amazonaws.com"
  DB_PORT: "1521"
  DB_SERVICE_NAME: "orcl"
  DB_USERNAME: "webform_db"
  DB_PASSWORD: "WebForm123!@#"

---
apiVersion: batch/v1
kind: Job
metadata:
  name: oracle-rds-connectivity-test
  namespace: default
spec:
  template:
    spec:
      restartPolicy: Never
      containers:
      - name: oracle-test
        image: python:3.9-slim
        imagePullPolicy: Always
        command: ["/bin/bash"]
        args: ["-c", "apt-get update && apt-get install -y wget unzip libaio1t64 && ln -sf /usr/lib/x86_64-linux-gnu/libaio.so.1t64 /usr/lib/x86_64-linux-gnu/libaio.so.1 && wget -q https://download.oracle.com/otn_software/linux/instantclient/1923000/instantclient-basic-linux.x64-*********.0dbru.zip -O /tmp/oracle.zip && unzip -q /tmp/oracle.zip -d /opt/oracle && echo '/opt/oracle/instantclient_19_23' > /etc/ld.so.conf.d/oracle-instantclient.conf && ldconfig && pip install cx_Oracle==8.3.0 && python /app/oracle-test.py"]
        volumeMounts:
        - name: test-script
          mountPath: /app
        env:
        - name: DB_CONNECTION
          value: "oracle"
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: oracle-db-secret
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: oracle-db-secret
              key: DB_PORT
        - name: DB_SERVICE_NAME
          valueFrom:
            secretKeyRef:
              name: oracle-db-secret
              key: DB_SERVICE_NAME
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: oracle-db-secret
              key: DB_USERNAME
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: oracle-db-secret
              key: DB_PASSWORD
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        env:
        - name: LD_LIBRARY_PATH
          value: "/opt/oracle/instantclient_19_23"
        - name: PATH
          value: "/opt/oracle/instantclient_19_23:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
      volumes:
      - name: test-script
        configMap:
          name: oracle-test-script
  backoffLimit: 3

---
apiVersion: v1
kind: Pod
metadata:
  name: oracle-rds-connectivity-test-pod
  namespace: default
spec:
  restartPolicy: Never
  containers:
  - name: oracle-test
    image: python:3.9-slim
    imagePullPolicy: Always
    command: ["/bin/bash"]
    args: ["-c", "apt-get update && apt-get install -y wget unzip libaio1t64 && ln -sf /usr/lib/x86_64-linux-gnu/libaio.so.1t64 /usr/lib/x86_64-linux-gnu/libaio.so.1 && wget -q https://download.oracle.com/otn_software/linux/instantclient/1923000/instantclient-basic-linux.x64-*********.0dbru.zip -O /tmp/oracle.zip && unzip -q /tmp/oracle.zip -d /opt/oracle && echo '/opt/oracle/instantclient_19_23' > /etc/ld.so.conf.d/oracle-instantclient.conf && ldconfig && pip install cx_Oracle==8.3.0 && python /app/oracle-test.py"]
    volumeMounts:
    - name: test-script
      mountPath: /app
    env:
    - name: DB_CONNECTION
      value: "oracle"
    - name: DB_HOST
      valueFrom:
        secretKeyRef:
          name: oracle-db-secret
          key: DB_HOST
    - name: DB_PORT
      valueFrom:
        secretKeyRef:
          name: oracle-db-secret
          key: DB_PORT
    - name: DB_SERVICE_NAME
      valueFrom:
        secretKeyRef:
          name: oracle-db-secret
          key: DB_SERVICE_NAME
    - name: DB_USERNAME
      valueFrom:
        secretKeyRef:
          name: oracle-db-secret
          key: DB_USERNAME
    - name: DB_PASSWORD
      valueFrom:
        secretKeyRef:
          name: oracle-db-secret
          key: DB_PASSWORD
    resources:
      requests:
        memory: "512Mi"
        cpu: "200m"
      limits:
        memory: "1Gi"
        cpu: "500m"
    env:
    - name: LD_LIBRARY_PATH
      value: "/opt/oracle/instantclient_19_23"
    - name: PATH
      value: "/opt/oracle/instantclient_19_23:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
  volumes:
  - name: test-script
    configMap:
      name: oracle-test-script
