apiVersion: v1
kind: Pod
metadata:
  name: aks-network-diagnostic
  namespace: default
spec:
  restartPolicy: Never
  hostNetwork: true  # Use host network to see actual node networking
  containers:
  - name: network-diagnostic
    image: ubuntu:22.04
    imagePullPolicy: Always
    command: ["/bin/bash"]
    args: 
    - "-c"
    - |
      apt-get update && apt-get install -y iproute2 iputils-ping netcat-openbsd curl telnet dnsutils
      
      echo "🔍 AKS VPN and Network Configuration Check"
      echo "=========================================="
      
      echo -e "\n=== System Information ==="
      echo "Hostname: $(hostname)"
      echo "Date: $(date)"
      
      echo -e "\n=== Kubernetes Environment ==="
      if [ -n "$KUBERNETES_SERVICE_HOST" ]; then
        echo "✅ Running inside Kubernetes cluster"
        echo "Kubernetes Service Host: $KUBERNETES_SERVICE_HOST"
      else
        echo "⚠️ Not detected as running in Kubernetes"
      fi
      
      echo -e "\n=== Network Interfaces ==="
      ip addr show | grep -E "^[0-9]+:|inet "
      
      echo -e "\n=== Routing Table ==="
      ip route show
      
      echo -e "\n=== DNS Configuration ==="
      cat /etc/resolv.conf
      
      echo -e "\n=== VPN Processes ==="
      ps aux | grep -i -E "(vpn|openvpn|wireguard|ipsec|strongswan|tunnel)" | grep -v grep || echo "No VPN processes found"
      
      echo -e "\n=== VPN Network Interfaces ==="
      ip link show | grep -E "(tun|tap|wg|ipsec|vpn)" || echo "No VPN interfaces found"
      
      echo -e "\n=== Environment Variables ==="
      env | grep -i -E "(vpn|proxy|network|dns|gateway)" || echo "No network-related environment variables found"
      
      echo -e "\n=== Proxy Configuration ==="
      echo "HTTP_PROXY: ${HTTP_PROXY:-Not set}"
      echo "HTTPS_PROXY: ${HTTPS_PROXY:-Not set}"
      echo "NO_PROXY: ${NO_PROXY:-Not set}"
      
      echo -e "\n=== Connectivity Tests ==="
      test_connectivity() {
        local host=$1
        local port=$2
        local description=$3
        
        if timeout 5 nc -z "$host" "$port" 2>/dev/null; then
          echo "✅ Can connect to $description ($host:$port)"
        else
          echo "❌ Cannot connect to $description ($host:$port)"
        fi
      }
      
      test_connectivity "*******" "53" "Google DNS"
      test_connectivity "*******" "53" "Cloudflare DNS"
      test_connectivity "google.com" "80" "Google HTTP"
      test_connectivity "google.com" "443" "Google HTTPS"
      test_connectivity "*************" "1521" "Oracle On-Premise"
      test_connectivity "takafull-test.c2hcooa00sjz.us-east-1.rds.amazonaws.com" "1521" "Oracle RDS"
      
      echo -e "\n=== Azure Metadata ==="
      curl -s -H "Metadata:true" "http://***************/metadata/instance/network?api-version=2021-02-01" 2>/dev/null | head -10 || echo "Azure metadata not accessible"
      
      echo -e "\n=== CNI Configuration ==="
      if [ -d "/etc/cni/net.d" ]; then
        echo "CNI configuration files:"
        ls -la /etc/cni/net.d/ 2>/dev/null || echo "Cannot access CNI directory"
      else
        echo "CNI directory not found"
      fi
      
      echo -e "\n=== Summary ==="
      echo "Network diagnostic completed at $(date)"
      
      # Keep pod running for manual inspection
      sleep 600
    securityContext:
      privileged: true
      capabilities:
        add:
        - NET_ADMIN
        - SYS_ADMIN
    resources:
      requests:
        memory: "256Mi"
        cpu: "100m"
      limits:
        memory: "512Mi"
        cpu: "200m"
