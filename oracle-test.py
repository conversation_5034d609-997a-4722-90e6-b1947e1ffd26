#!/usr/bin/env python3
"""
Oracle Database Connectivity Test for AKS
Tests connection to Oracle database using provided credentials
"""

import os
import sys
import time
import logging
from datetime import datetime

try:
    import cx_Oracle
except ImportError:
    print("ERROR: cx_Oracle library not found. Please install it first.")
    print("Run: pip install cx_Oracle")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_db_config():
    """Get database configuration from environment variables"""
    config = {
        'host': os.getenv('DB_HOST', '*************'),
        'port': int(os.getenv('DB_PORT', '1521')),
        'service_name': os.getenv('DB_SERVICE_NAME', 'TEST18C'),
        'username': os.getenv('DB_USERNAME', 'dev_ESERVICES'),
        'password': os.getenv('DB_PASSWORD', 'dev_ESERVICES')
    }
    
    logger.info(f"Database config: {config['username']}@{config['host']}:{config['port']}/{config['service_name']}")
    return config

def test_oracle_connection():
    """Test Oracle database connection"""
    config = get_db_config()
    
    # Create connection string
    dsn = cx_Oracle.makedsn(
        host=config['host'],
        port=config['port'],
        service_name=config['service_name']
    )
    
    connection = None
    try:
        logger.info("Attempting to connect to Oracle database...")
        start_time = time.time()
        
        connection = cx_Oracle.connect(
            user=config['username'],
            password=config['password'],
            dsn=dsn
        )
        
        connect_time = time.time() - start_time
        logger.info(f"✅ Successfully connected to Oracle database in {connect_time:.2f} seconds")
        
        # Test basic query
        cursor = connection.cursor()
        cursor.execute("SELECT SYSDATE FROM DUAL")
        result = cursor.fetchone()
        
        logger.info(f"✅ Database query successful. Current database time: {result[0]}")
        
        # Get database version
        cursor.execute("SELECT BANNER FROM V$VERSION WHERE ROWNUM = 1")
        version = cursor.fetchone()
        logger.info(f"✅ Database version: {version[0]}")
        
        cursor.close()
        
        return True
        
    except cx_Oracle.DatabaseError as e:
        error, = e.args
        logger.error(f"❌ Database connection failed: {error.message}")
        return False
        
    except Exception as e:
        logger.error(f"❌ Unexpected error: {str(e)}")
        return False
        
    finally:
        if connection:
            connection.close()
            logger.info("Database connection closed")

def main():
    """Main function"""
    logger.info("Starting Oracle Database Connectivity Test")
    logger.info(f"Test started at: {datetime.now()}")
    
    success = test_oracle_connection()
    
    if success:
        logger.info("🎉 Oracle database connectivity test PASSED")
        sys.exit(0)
    else:
        logger.error("💥 Oracle database connectivity test FAILED")
        sys.exit(1)

if __name__ == "__main__":
    main()
