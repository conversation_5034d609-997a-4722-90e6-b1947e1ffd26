# Oracle Database Connectivity Test for AKS

This project provides a simple way to test Oracle database connectivity from Azure Kubernetes Service (AKS).

## Database Configuration

The test uses the following Oracle database configuration:
- **Host**: *************
- **Port**: 1521
- **Service Name**: TEST18C
- **Username**: dev_ESERVICES
- **Password**: dev_ESERVICES

## Files Overview

- `oracle-test.py` - Python script that tests Oracle database connectivity
- `Dockerfile` - Container image definition with Oracle Instant Client
- `requirements.txt` - Python dependencies
- `k8s-oracle-test.yaml` - Kubernetes manifests (Secret, Job, Pod)
- `deploy.sh` - Automated deployment script for AKS
- `test-local.sh` - Local testing script
- `README.md` - This documentation

## Prerequisites

### For AKS Deployment
1. Docker installed and running
2. kubectl configured to connect to your AKS cluster
3. Azure CLI (optional, for AKS management)

### For Local Testing
1. Python 3.x installed
2. pip package manager

## Usage

### Option 1: Deploy to AKS (Recommended)

1. Make the deployment script executable:
   ```bash
   chmod +x deploy.sh
   ```

2. Run the deployment:
   ```bash
   ./deploy.sh
   ```

The script will:
- Build the Docker image with Oracle Instant Client
- Deploy the Kubernetes resources
- Run the connectivity test as a Job
- Display the test results

### Option 2: Test Locally

1. Make the local test script executable:
   ```bash
   chmod +x test-local.sh
   ```

2. Run the local test:
   ```bash
   ./test-local.sh
   ```

### Option 3: Manual Kubernetes Deployment

1. Build the Docker image:
   ```bash
   docker build -t oracle-test:latest .
   ```

2. Apply Kubernetes manifests:
   ```bash
   kubectl apply -f k8s-oracle-test.yaml
   ```

3. Check job status:
   ```bash
   kubectl get jobs
   ```

4. View logs:
   ```bash
   kubectl logs job/oracle-connectivity-test
   ```

## Test Results

The test will perform the following checks:
- ✅ Database connection establishment
- ✅ Basic SQL query execution (`SELECT SYSDATE FROM DUAL`)
- ✅ Database version retrieval
- ✅ Connection timing measurement

## Troubleshooting

### Common Issues

1. **Connection Timeout**
   - Check if the Oracle database server is accessible from AKS
   - Verify network security groups and firewall rules
   - Ensure the database service is running

2. **Authentication Failed**
   - Verify username and password are correct
   - Check if the user account is not locked
   - Ensure the user has necessary privileges

3. **Service Name Not Found**
   - Verify the Oracle service name (TEST18C)
   - Check if the database instance is running
   - Try using SID instead of service name if needed

4. **Docker Build Issues**
   - Ensure Docker has internet access to download Oracle Instant Client
   - Check if the Oracle Instant Client download URL is accessible

### Viewing Detailed Logs

```bash
# Get pod name
kubectl get pods -l job-name=oracle-connectivity-test

# View detailed logs
kubectl logs <pod-name>

# Describe pod for more details
kubectl describe pod <pod-name>
```

### Cleanup

To remove all created resources:
```bash
kubectl delete -f k8s-oracle-test.yaml
```

## Security Notes

- Database credentials are stored in Kubernetes Secrets
- Consider using Azure Key Vault for production environments
- The test container runs with minimal privileges
- Network policies should be configured to restrict database access

## Customization

To modify the database configuration, update the values in:
- `k8s-oracle-test.yaml` (Secret section)
- `test-local.sh` (environment variables)
- `oracle-test.py` (default values)

## Support

If you encounter issues:
1. Check the pod logs for detailed error messages
2. Verify network connectivity from AKS to the Oracle database
3. Ensure the Oracle database is configured to accept connections
4. Check Oracle database logs for connection attempts
