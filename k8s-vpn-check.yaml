apiVersion: v1
kind: ConfigMap
metadata:
  name: aks-vpn-check-script
  namespace: default
data:
  aks-vpn-check.sh: |
    #!/bin/bash

    echo "🔍 AKS VPN and Network Configuration Check"
    echo "=========================================="

    # Colors for output
    RED='\033[0;31m'
    GREEN='\033[0;32m'
    YELLOW='\033[1;33m'
    BLUE='\033[0;34m'
    NC='\033[0m' # No Color

    print_header() {
        echo -e "\n${BLUE}=== $1 ===${NC}"
    }

    print_success() {
        echo -e "${GREEN}✅ $1${NC}"
    }

    print_warning() {
        echo -e "${YELLOW}⚠️  $1${NC}"
    }

    print_error() {
        echo -e "${RED}❌ $1${NC}"
    }

    print_info() {
        echo -e "${BLUE}ℹ️  $1${NC}"
    }

    # Basic system information
    print_header "System Information"
    echo "Hostname: $(hostname)"
    echo "Date: $(date)"
    echo "Uptime: $(uptime)"

    # Check if we're in Kubernetes
    print_header "Kubernetes Environment Check"
    if [ -n "$KUBERNETES_SERVICE_HOST" ]; then
        print_success "Running inside Kubernetes cluster"
        echo "Kubernetes Service Host: $KUBERNETES_SERVICE_HOST"
        echo "Kubernetes Service Port: $KUBERNETES_SERVICE_PORT"
    else
        print_warning "Not detected as running in Kubernetes"
    fi

    # Network interface information
    print_header "Network Interfaces"
    ip addr show | grep -E "^[0-9]+:|inet " | while read line; do
        echo "$line"
    done

    # Routing table
    print_header "Routing Table"
    echo "Main routing table:"
    ip route show

    echo -e "\nAll routing tables:"
    ip route show table all | head -20

    # DNS Configuration
    print_header "DNS Configuration"
    echo "DNS servers from /etc/resolv.conf:"
    cat /etc/resolv.conf

    # Check for VPN-related processes
    print_header "VPN-Related Processes"
    echo "Checking for VPN processes..."
    ps aux | grep -i -E "(vpn|openvpn|wireguard|ipsec|strongswan|tunnel)" | grep -v grep || echo "No VPN processes found"

    # Check for VPN-related network interfaces
    print_header "VPN Network Interfaces"
    echo "Checking for VPN interfaces (tun, tap, wg, ipsec)..."
    ip link show | grep -E "(tun|tap|wg|ipsec|vpn)" || echo "No VPN interfaces found"

    # Check for VPN configuration files
    print_header "VPN Configuration Files"
    echo "Checking for VPN config files..."
    find /etc -name "*vpn*" -o -name "*openvpn*" -o -name "*wireguard*" -o -name "*ipsec*" 2>/dev/null | head -10 || echo "No VPN config files found in /etc"

    # Check network namespaces
    print_header "Network Namespaces"
    echo "Available network namespaces:"
    ip netns list 2>/dev/null || echo "No additional network namespaces or insufficient permissions"

    # Check iptables rules (if available)
    print_header "Firewall Rules"
    echo "Checking iptables rules..."
    if command -v iptables >/dev/null 2>&1; then
        echo "NAT table rules:"
        iptables -t nat -L -n 2>/dev/null | head -20 || echo "Cannot access iptables NAT rules"
        
        echo -e "\nFilter table rules:"
        iptables -t filter -L -n 2>/dev/null | head -20 || echo "Cannot access iptables filter rules"
    else
        echo "iptables not available"
    fi

    # Check for Azure-specific networking
    print_header "Azure Networking Information"
    echo "Checking Azure metadata (if available)..."
    if command -v curl >/dev/null 2>&1; then
        echo "Azure Instance Metadata:"
        curl -s -H "Metadata:true" "http://***************/metadata/instance/network?api-version=2021-02-01" 2>/dev/null | head -10 || echo "Azure metadata not accessible"
    else
        echo "curl not available for metadata check"
    fi

    # Check environment variables for VPN/network config
    print_header "Environment Variables"
    echo "Network-related environment variables:"
    env | grep -i -E "(vpn|proxy|network|dns|gateway)" | head -10 || echo "No network-related environment variables found"

    # Check for proxy configuration
    print_header "Proxy Configuration"
    echo "HTTP/HTTPS proxy settings:"
    echo "HTTP_PROXY: ${HTTP_PROXY:-Not set}"
    echo "HTTPS_PROXY: ${HTTPS_PROXY:-Not set}"
    echo "NO_PROXY: ${NO_PROXY:-Not set}"
    echo "http_proxy: ${http_proxy:-Not set}"
    echo "https_proxy: ${https_proxy:-Not set}"
    echo "no_proxy: ${no_proxy:-Not set}"

    # Test connectivity to various endpoints
    print_header "Connectivity Tests"
    echo "Testing connectivity to various endpoints..."

    test_connectivity() {
        local host=$1
        local port=$2
        local description=$3
        
        if command -v nc >/dev/null 2>&1; then
            if timeout 5 nc -z "$host" "$port" 2>/dev/null; then
                print_success "Can connect to $description ($host:$port)"
            else
                print_error "Cannot connect to $description ($host:$port)"
            fi
        elif command -v telnet >/dev/null 2>&1; then
            if timeout 5 telnet "$host" "$port" </dev/null 2>/dev/null | grep -q "Connected"; then
                print_success "Can connect to $description ($host:$port)"
            else
                print_error "Cannot connect to $description ($host:$port)"
            fi
        else
            print_warning "No connectivity testing tools available (nc/telnet)"
        fi
    }

    # Test various endpoints
    test_connectivity "*******" "53" "Google DNS"
    test_connectivity "*******" "53" "Cloudflare DNS"
    test_connectivity "google.com" "80" "Google HTTP"
    test_connectivity "google.com" "443" "Google HTTPS"
    test_connectivity "*************" "1521" "Oracle On-Premise"
    test_connectivity "takafull-test.c2hcooa00sjz.us-east-1.rds.amazonaws.com" "1521" "Oracle RDS"

    # Check for Azure CNI or other network plugins
    print_header "Container Network Interface (CNI)"
    echo "Checking for CNI configuration..."
    if [ -d "/etc/cni/net.d" ]; then
        echo "CNI configuration files:"
        ls -la /etc/cni/net.d/ 2>/dev/null || echo "Cannot access CNI directory"
        
        echo -e "\nCNI config content:"
        find /etc/cni/net.d -name "*.conf" -o -name "*.json" | head -3 | while read file; do
            echo "=== $file ==="
            cat "$file" 2>/dev/null | head -10
        done
    else
        echo "CNI directory not found"
    fi

    # Check for service mesh (Istio, Linkerd, etc.)
    print_header "Service Mesh Detection"
    echo "Checking for service mesh components..."
    if [ -f "/etc/istio-proxy" ] || [ -d "/etc/istio" ]; then
        print_success "Istio detected"
    elif ps aux | grep -q "linkerd"; then
        print_success "Linkerd detected"
    elif ps aux | grep -q "envoy"; then
        print_success "Envoy proxy detected"
    else
        echo "No service mesh detected"
    fi

    # Check mounted volumes for network config
    print_header "Mounted Volumes"
    echo "Checking mounted volumes for network configuration..."
    mount | grep -E "(secret|config)" | head -10 || echo "No relevant mounted volumes found"

    # Final summary
    print_header "Summary"
    echo "VPN/Network configuration check completed."
    echo "Review the above output for:"
    echo "- VPN interfaces (tun/tap/wg)"
    echo "- VPN processes"
    echo "- Routing table entries"
    echo "- Connectivity test results"
    echo "- Azure networking configuration"

    echo -e "\n${GREEN}Check completed at $(date)${NC}"

---
apiVersion: batch/v1
kind: Job
metadata:
  name: aks-vpn-network-check
  namespace: default
spec:
  template:
    spec:
      restartPolicy: Never
      hostNetwork: true  # Use host network to see actual node networking
      containers:
      - name: vpn-network-check
        image: ubuntu:22.04
        imagePullPolicy: Always
        command: ["/bin/bash"]
        args: ["-c", "apt-get update && apt-get install -y iproute2 iputils-ping netcat-openbsd curl telnet dnsutils && chmod +x /scripts/aks-vpn-check.sh && /scripts/aks-vpn-check.sh"]
        volumeMounts:
        - name: vpn-check-script
          mountPath: /scripts
        securityContext:
          privileged: true  # Need privileged access to check network configuration
          capabilities:
            add:
            - NET_ADMIN
            - SYS_ADMIN
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
      volumes:
      - name: vpn-check-script
        configMap:
          name: aks-vpn-check-script
          defaultMode: 0755
  backoffLimit: 1

---
apiVersion: v1
kind: Pod
metadata:
  name: aks-vpn-network-check-pod
  namespace: default
spec:
  restartPolicy: Never
  hostNetwork: true  # Use host network to see actual node networking
  containers:
  - name: vpn-network-check
    image: ubuntu:22.04
    imagePullPolicy: Always
    command: ["/bin/bash"]
    args: ["-c", "apt-get update && apt-get install -y iproute2 iputils-ping netcat-openbsd curl telnet dnsutils && chmod +x /scripts/aks-vpn-check.sh && /scripts/aks-vpn-check.sh && sleep 300"]
    volumeMounts:
    - name: vpn-check-script
      mountPath: /scripts
    securityContext:
      privileged: true  # Need privileged access to check network configuration
      capabilities:
        add:
        - NET_ADMIN
        - SYS_ADMIN
    resources:
      requests:
        memory: "256Mi"
        cpu: "100m"
      limits:
        memory: "512Mi"
        cpu: "200m"
  volumes:
  - name: vpn-check-script
    configMap:
      name: aks-vpn-check-script
      defaultMode: 0755
