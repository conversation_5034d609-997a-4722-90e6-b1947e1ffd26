apiVersion: v1
kind: ConfigMap
metadata:
  name: oracle-rds-schema-check-script
  namespace: default
data:
  oracle-rds-schema-check.py: |
    #!/usr/bin/env python3
    """
    Oracle RDS Database Schema Diagnostic Tool
    Checks for missing tables/views and provides detailed schema information
    """

    import os
    import sys
    import time
    import logging
    from datetime import datetime

    try:
        import cx_Oracle
    except ImportError:
        print("ERROR: cx_Oracle library not found. Please install it first.")
        print("Run: pip install cx_Oracle")
        sys.exit(1)

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)

    def get_db_config():
        """Get database configuration from environment variables"""
        config = {
            'host': os.getenv('DB_HOST', 'takafull-test.c2hcooa00sjz.us-east-1.rds.amazonaws.com'),
            'port': int(os.getenv('DB_PORT', '1521')),
            'service_name': os.getenv('DB_SERVICE_NAME', 'orcl'),
            'username': os.getenv('DB_USERNAME', 'webform_db'),
            'password': os.getenv('DB_PASSWORD', 'WebForm123!@#')
        }
        
        logger.info(f"Database config: {config['username']}@{config['host']}:{config['port']}/{config['service_name']}")
        return config

    def check_oracle_schema():
        """Check Oracle database schema and missing tables/views"""
        config = get_db_config()
        
        # Create connection string
        dsn = cx_Oracle.makedsn(
            host=config['host'],
            port=config['port'],
            service_name=config['service_name']
        )
        
        connection = None
        try:
            logger.info("Connecting to Oracle RDS database...")
            connection = cx_Oracle.connect(
                user=config['username'],
                password=config['password'],
                dsn=dsn
            )
            
            logger.info("✅ Successfully connected to Oracle RDS database")
            cursor = connection.cursor()
            
            # Check current user and schema
            cursor.execute("SELECT USER FROM DUAL")
            current_user = cursor.fetchone()[0]
            logger.info(f"📋 Current user: {current_user}")
            
            # Check database version and instance
            cursor.execute("SELECT BANNER FROM V$VERSION WHERE ROWNUM = 1")
            version = cursor.fetchone()[0]
            logger.info(f"🗄️  Database version: {version}")
            
            # List all tables accessible to current user
            logger.info("📊 Checking accessible tables...")
            cursor.execute("""
                SELECT table_name, owner, num_rows, last_analyzed
                FROM all_tables 
                WHERE owner IN (USER, 'WEBFORM_DB', 'PUBLIC', 'SYS', 'SYSTEM')
                ORDER BY owner, table_name
            """)
            
            tables = cursor.fetchall()
            logger.info(f"📈 Found {len(tables)} accessible tables")
            
            # Group tables by owner
            tables_by_owner = {}
            for table_name, owner, num_rows, last_analyzed in tables:
                if owner not in tables_by_owner:
                    tables_by_owner[owner] = []
                tables_by_owner[owner].append((table_name, num_rows, last_analyzed))
            
            # Display tables by owner
            for owner, table_list in tables_by_owner.items():
                logger.info(f"📁 Schema '{owner}': {len(table_list)} tables")
                for table, rows, analyzed in sorted(table_list)[:15]:  # Show first 15 tables
                    row_info = f"({rows} rows)" if rows else "(no stats)"
                    logger.info(f"   - {table} {row_info}")
                if len(table_list) > 15:
                    logger.info(f"   ... and {len(table_list) - 15} more tables")
            
            # List all views accessible to current user
            logger.info("👁️  Checking accessible views...")
            cursor.execute("""
                SELECT view_name, owner
                FROM all_views 
                WHERE owner IN (USER, 'WEBFORM_DB', 'PUBLIC', 'SYS', 'SYSTEM')
                ORDER BY owner, view_name
            """)
            
            views = cursor.fetchall()
            logger.info(f"👀 Found {len(views)} accessible views")
            
            # Group views by owner
            views_by_owner = {}
            for view_name, owner in views:
                if owner not in views_by_owner:
                    views_by_owner[owner] = []
                views_by_owner[owner].append(view_name)
            
            # Display views by owner
            for owner, view_list in views_by_owner.items():
                logger.info(f"📁 Schema '{owner}' views: {len(view_list)} views")
                for view in sorted(view_list)[:10]:  # Show first 10 views
                    logger.info(f"   - {view}")
                if len(view_list) > 10:
                    logger.info(f"   ... and {len(view_list) - 10} more views")
            
            # Check for specific missing tables/views from the error logs
            missing_objects = [
                'V_POL_USE',
                'IPROD_LOCATION',
                'IPROD_ELEVATOR_BUIDING_CATEGORY'
            ]
            
            logger.info("🔍 Checking for specific missing tables/views...")
            for object_name in missing_objects:
                # Check if it's a table
                cursor.execute("""
                    SELECT COUNT(*), owner 
                    FROM all_tables 
                    WHERE table_name = :object_name
                    AND owner IN (USER, 'WEBFORM_DB', 'PUBLIC', 'SYS', 'SYSTEM')
                    GROUP BY owner
                """, {'object_name': object_name})
                
                table_results = cursor.fetchall()
                
                # Check if it's a view
                cursor.execute("""
                    SELECT COUNT(*), owner 
                    FROM all_views 
                    WHERE view_name = :object_name
                    AND owner IN (USER, 'WEBFORM_DB', 'PUBLIC', 'SYS', 'SYSTEM')
                    GROUP BY owner
                """, {'object_name': object_name})
                
                view_results = cursor.fetchall()
                
                if table_results:
                    for count, owner in table_results:
                        logger.info(f"✅ Table '{object_name}' exists in schema '{owner}'")
                        
                        # Check if table has data
                        try:
                            cursor.execute(f'SELECT COUNT(*) FROM "{owner}"."{object_name}"')
                            row_count = cursor.fetchone()[0]
                            logger.info(f"   📊 Row count: {row_count}")
                        except Exception as e:
                            logger.warning(f"   ⚠️  Cannot access table data: {e}")
                            
                elif view_results:
                    for count, owner in view_results:
                        logger.info(f"✅ View '{object_name}' exists in schema '{owner}'")
                        
                        # Try to query the view
                        try:
                            cursor.execute(f'SELECT COUNT(*) FROM "{owner}"."{object_name}"')
                            row_count = cursor.fetchone()[0]
                            logger.info(f"   📊 Row count: {row_count}")
                        except Exception as e:
                            logger.warning(f"   ⚠️  Cannot access view data: {e}")
                            
                else:
                    logger.error(f"❌ Object '{object_name}' NOT FOUND (neither table nor view)")
                    
                    # Search for similar object names
                    cursor.execute("""
                        SELECT 'TABLE' as object_type, table_name, owner 
                        FROM all_tables 
                        WHERE table_name LIKE :pattern
                        AND owner IN (USER, 'WEBFORM_DB', 'PUBLIC', 'SYS', 'SYSTEM')
                        UNION ALL
                        SELECT 'VIEW' as object_type, view_name, owner 
                        FROM all_views 
                        WHERE view_name LIKE :pattern
                        AND owner IN (USER, 'WEBFORM_DB', 'PUBLIC', 'SYS', 'SYSTEM')
                        ORDER BY object_type, owner, table_name
                    """, {'pattern': f'%{object_name.split("_")[-1]}%'})
                    
                    similar_objects = cursor.fetchall()
                    if similar_objects:
                        logger.info(f"   🔍 Similar objects found:")
                        for obj_type, obj_name, obj_owner in similar_objects[:10]:
                            logger.info(f"      - {obj_type}: {obj_owner}.{obj_name}")
            
            cursor.close()
            return True
            
        except cx_Oracle.DatabaseError as e:
            error, = e.args
            logger.error(f"❌ Database error: {error.message}")
            return False
            
        except Exception as e:
            logger.error(f"❌ Unexpected error: {str(e)}")
            return False
            
        finally:
            if connection:
                connection.close()
                logger.info("Database connection closed")

    def main():
        """Main function"""
        logger.info("Starting Oracle RDS Database Schema Check")
        logger.info(f"Check started at: {datetime.now()}")
        
        success = check_oracle_schema()
        
        if success:
            logger.info("🎉 Oracle RDS database schema check completed")
            sys.exit(0)
        else:
            logger.error("💥 Oracle RDS database schema check failed")
            sys.exit(1)

    if __name__ == "__main__":
        main()

---
apiVersion: v1
kind: Secret
metadata:
  name: oracle-rds-db-secret
  namespace: default
type: Opaque
stringData:
  DB_HOST: "takafull-test.c2hcooa00sjz.us-east-1.rds.amazonaws.com"
  DB_PORT: "1521"
  DB_SERVICE_NAME: "orcl"
  DB_USERNAME: "webform_db"
  DB_PASSWORD: "WebForm123!@#"

---
apiVersion: batch/v1
kind: Job
metadata:
  name: oracle-rds-schema-check
  namespace: default
spec:
  template:
    spec:
      restartPolicy: Never
      containers:
      - name: rds-schema-check
        image: python:3.9-slim
        imagePullPolicy: Always
        command: ["/bin/bash"]
        args: ["-c", "apt-get update && apt-get install -y wget unzip libaio1t64 && ln -sf /usr/lib/x86_64-linux-gnu/libaio.so.1t64 /usr/lib/x86_64-linux-gnu/libaio.so.1 && wget -q https://download.oracle.com/otn_software/linux/instantclient/1923000/instantclient-basic-linux.x64-*********.0dbru.zip -O /tmp/oracle.zip && unzip -q /tmp/oracle.zip -d /opt/oracle && echo '/opt/oracle/instantclient_19_23' > /etc/ld.so.conf.d/oracle-instantclient.conf && ldconfig && pip install cx_Oracle==8.3.0 && python /app/oracle-rds-schema-check.py"]
        volumeMounts:
        - name: rds-schema-check-script
          mountPath: /app
        env:
        - name: DB_CONNECTION
          value: "oracle"
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: oracle-rds-db-secret
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: oracle-rds-db-secret
              key: DB_PORT
        - name: DB_SERVICE_NAME
          valueFrom:
            secretKeyRef:
              name: oracle-rds-db-secret
              key: DB_SERVICE_NAME
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: oracle-rds-db-secret
              key: DB_USERNAME
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: oracle-rds-db-secret
              key: DB_PASSWORD
        - name: LD_LIBRARY_PATH
          value: "/opt/oracle/instantclient_19_23"
        - name: PATH
          value: "/opt/oracle/instantclient_19_23:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: rds-schema-check-script
        configMap:
          name: oracle-rds-schema-check-script
  backoffLimit: 1
